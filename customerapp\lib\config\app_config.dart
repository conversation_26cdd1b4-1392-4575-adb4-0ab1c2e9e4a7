import 'dart:io';

class AppConfig {
  // Environment
  static const String environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  static bool get isDevelopment => environment == 'development';
  static bool get isProduction => environment == 'production';
  static bool get isStaging => environment == 'staging';

  // API Configuration
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://mart.wafipos.com/api',
  );
  
  static const String signalingServerUrl = String.fromEnvironment(
    'SIGNALING_SERVER_URL',
    defaultValue: 'ws://localhost:3001',
  );

  // Security Configuration
  static const bool enableRequestSigning = bool.fromEnvironment(
    'ENABLE_REQUEST_SIGNING',
    defaultValue: true,
  );
  
  static const bool enableResponseValidation = bool.fromEnvironment(
    'ENABLE_RESPONSE_VALIDATION',
    defaultValue: true,
  );
  
  static const String apiKey = String.fromEnvironment('API_KEY', defaultValue: '');
  static const String secretKey = String.fromEnvironment('SECRET_KEY', defaultValue: '');

  // Performance Configuration
  static const int requestTimeoutSeconds = int.fromEnvironment(
    'REQUEST_TIMEOUT_SECONDS',
    defaultValue: 30,
  );
  
  static const int maxRetries = int.fromEnvironment('MAX_RETRIES', defaultValue: 3);
  static const int maxRequestsPerMinute = int.fromEnvironment(
    'MAX_REQUESTS_PER_MINUTE',
    defaultValue: 60,
  );

  // WebRTC Configuration
  static const List<String> stunServers = [
    'stun:stun.l.google.com:19302',
    'stun:stun1.l.google.com:19302',
    'stun:stun2.l.google.com:19302',
  ];

  static const String turnServerUrl = String.fromEnvironment('TURN_SERVER_URL', defaultValue: '');
  static const String turnUsername = String.fromEnvironment('TURN_USERNAME', defaultValue: '');
  static const String turnCredential = String.fromEnvironment('TURN_CREDENTIAL', defaultValue: '');

  // Video Quality Settings
  static const Map<String, Map<String, dynamic>> videoQualitySettings = {
    'low': {
      'width': 320,
      'height': 240,
      'frameRate': 15,
      'bitrate': 150000, // 150 kbps
    },
    'medium': {
      'width': 640,
      'height': 480,
      'frameRate': 24,
      'bitrate': 500000, // 500 kbps
    },
    'high': {
      'width': 1280,
      'height': 720,
      'frameRate': 30,
      'bitrate': 1500000, // 1.5 Mbps
    },
  };

  // Audio Quality Settings
  static const Map<String, Map<String, dynamic>> audioQualitySettings = {
    'low': {
      'sampleRate': 16000,
      'bitrate': 32000, // 32 kbps
      'channelCount': 1,
    },
    'high': {
      'sampleRate': 48000,
      'bitrate': 128000, // 128 kbps
      'channelCount': 2,
    },
  };

  // Call Configuration
  static const int callTimeoutSeconds = int.fromEnvironment(
    'CALL_TIMEOUT_SECONDS',
    defaultValue: 60,
  );
  
  static const int maxCallDurationSeconds = int.fromEnvironment(
    'MAX_CALL_DURATION_SECONDS',
    defaultValue: 3600, // 1 hour
  );
  
  static const int networkTimeoutSeconds = int.fromEnvironment(
    'NETWORK_TIMEOUT_SECONDS',
    defaultValue: 30,
  );

  // Logging Configuration
  static const bool enableDetailedLogging = bool.fromEnvironment(
    'ENABLE_DETAILED_LOGGING',
    defaultValue: true,
  );
  
  static const bool enablePerformanceLogging = bool.fromEnvironment(
    'ENABLE_PERFORMANCE_LOGGING',
    defaultValue: true,
  );
  
  static const bool enableSecurityLogging = bool.fromEnvironment(
    'ENABLE_SECURITY_LOGGING',
    defaultValue: true,
  );

  // Error Handling Configuration
  static const bool enableCrashReporting = bool.fromEnvironment(
    'ENABLE_CRASH_REPORTING',
    defaultValue: true,
  );
  
  static const bool enableErrorAnalytics = bool.fromEnvironment(
    'ENABLE_ERROR_ANALYTICS',
    defaultValue: true,
  );
  
  static const int maxErrorHistorySize = int.fromEnvironment(
    'MAX_ERROR_HISTORY_SIZE',
    defaultValue: 100,
  );

  // Performance Monitoring
  static const bool enablePerformanceMonitoring = bool.fromEnvironment(
    'ENABLE_PERFORMANCE_MONITORING',
    defaultValue: true,
  );
  
  static const int performanceMetricsIntervalSeconds = int.fromEnvironment(
    'PERFORMANCE_METRICS_INTERVAL_SECONDS',
    defaultValue: 2,
  );
  
  static const int qualityAdaptationIntervalSeconds = int.fromEnvironment(
    'QUALITY_ADAPTATION_INTERVAL_SECONDS',
    defaultValue: 5,
  );

  // Network Quality Thresholds
  static const Map<String, Map<String, double>> networkQualityThresholds = {
    'excellent': {
      'maxPacketLoss': 1.0, // 1%
      'maxRtt': 50.0, // 50ms
      'minBitrate': 1000000.0, // 1 Mbps
    },
    'good': {
      'maxPacketLoss': 2.0, // 2%
      'maxRtt': 150.0, // 150ms
      'minBitrate': 500000.0, // 500 kbps
    },
    'fair': {
      'maxPacketLoss': 5.0, // 5%
      'maxRtt': 300.0, // 300ms
      'minBitrate': 200000.0, // 200 kbps
    },
    'poor': {
      'maxPacketLoss': 10.0, // 10%
      'maxRtt': 500.0, // 500ms
      'minBitrate': 100000.0, // 100 kbps
    },
  };

  // Adaptive Quality Settings
  static const bool enableAdaptiveQuality = bool.fromEnvironment(
    'ENABLE_ADAPTIVE_QUALITY',
    defaultValue: true,
  );
  
  static const bool enableBandwidthAdaptation = bool.fromEnvironment(
    'ENABLE_BANDWIDTH_ADAPTATION',
    defaultValue: true,
  );
  
  static const bool enableFrameRateAdaptation = bool.fromEnvironment(
    'ENABLE_FRAMERATE_ADAPTATION',
    defaultValue: true,
  );

  // Cache Configuration
  static const int cacheMaxSize = int.fromEnvironment(
    'CACHE_MAX_SIZE',
    defaultValue: 50 * 1024 * 1024, // 50MB
  );
  
  static const int cacheExpirationHours = int.fromEnvironment(
    'CACHE_EXPIRATION_HOURS',
    defaultValue: 24,
  );

  // Analytics Configuration
  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: true,
  );
  
  static const String analyticsApiKey = String.fromEnvironment(
    'ANALYTICS_API_KEY',
    defaultValue: '',
  );

  // Push Notifications
  static const bool enablePushNotifications = bool.fromEnvironment(
    'ENABLE_PUSH_NOTIFICATIONS',
    defaultValue: true,
  );
  
  static const String fcmSenderId = String.fromEnvironment(
    'FCM_SENDER_ID',
    defaultValue: '',
  );

  // Feature Flags
  static const bool enableVideoRecording = bool.fromEnvironment(
    'ENABLE_VIDEO_RECORDING',
    defaultValue: false,
  );
  
  static const bool enableScreenSharing = bool.fromEnvironment(
    'ENABLE_SCREEN_SHARING',
    defaultValue: false,
  );
  
  static const bool enableChatDuringCall = bool.fromEnvironment(
    'ENABLE_CHAT_DURING_CALL',
    defaultValue: true,
  );

  // Platform-specific settings
  static Map<String, dynamic> get platformSpecificSettings {
    if (Platform.isIOS) {
      return {
        'backgroundModes': ['voip', 'audio'],
        'cameraUsageDescription': 'This app needs camera access for video calls',
        'microphoneUsageDescription': 'This app needs microphone access for video calls',
      };
    } else if (Platform.isAndroid) {
      return {
        'permissions': [
          'android.permission.CAMERA',
          'android.permission.RECORD_AUDIO',
          'android.permission.INTERNET',
          'android.permission.ACCESS_NETWORK_STATE',
          'android.permission.WAKE_LOCK',
        ],
        'foregroundServiceType': 'camera|microphone',
      };
    }
    return {};
  }

  // Get WebRTC configuration
  static Map<String, dynamic> get webrtcConfiguration {
    final iceServers = <Map<String, dynamic>>[
      ...stunServers.map((url) => {'urls': url}),
    ];

    // Add TURN server if configured
    if (turnServerUrl.isNotEmpty) {
      iceServers.add({
        'urls': turnServerUrl,
        'username': turnUsername,
        'credential': turnCredential,
      });
    }

    return {
      'iceServers': iceServers,
      'iceCandidatePoolSize': 10,
      'bundlePolicy': 'max-bundle',
      'rtcpMuxPolicy': 'require',
      'iceTransportPolicy': 'all',
      'enableDtlsSrtp': true,
      'sdpSemantics': 'unified-plan',
    };
  }

  // Get media constraints based on quality
  static Map<String, dynamic> getMediaConstraints(String quality) {
    final videoSettings = videoQualitySettings[quality] ?? videoQualitySettings['medium']!;
    final audioSettings = audioQualitySettings['high']!;

    return {
      'audio': {
        'echoCancellation': true,
        'noiseSuppression': true,
        'autoGainControl': true,
        'sampleRate': audioSettings['sampleRate'],
        'channelCount': audioSettings['channelCount'],
      },
      'video': {
        'facingMode': 'user',
        'width': {'ideal': videoSettings['width']},
        'height': {'ideal': videoSettings['height']},
        'frameRate': {'ideal': videoSettings['frameRate']},
      },
    };
  }

  // Get security configuration
  static Map<String, dynamic> get securityConfig {
    return {
      'enableRequestSigning': enableRequestSigning,
      'enableResponseValidation': enableResponseValidation,
      'apiKey': apiKey,
      'secretKey': secretKey,
      'requestTimeout': Duration(seconds: requestTimeoutSeconds),
      'maxRetries': maxRetries,
    };
  }

  // Get performance configuration
  static Map<String, dynamic> get performanceConfig {
    return {
      'enableMonitoring': enablePerformanceMonitoring,
      'metricsInterval': Duration(seconds: performanceMetricsIntervalSeconds),
      'qualityAdaptationInterval': Duration(seconds: qualityAdaptationIntervalSeconds),
      'enableAdaptiveQuality': enableAdaptiveQuality,
      'enableBandwidthAdaptation': enableBandwidthAdaptation,
      'enableFrameRateAdaptation': enableFrameRateAdaptation,
    };
  }

  // Get error handling configuration
  static Map<String, dynamic> get errorHandlingConfig {
    return {
      'enableCrashReporting': enableCrashReporting,
      'enableErrorAnalytics': enableErrorAnalytics,
      'maxErrorHistorySize': maxErrorHistorySize,
      'enableDetailedLogging': enableDetailedLogging,
      'enablePerformanceLogging': enablePerformanceLogging,
      'enableSecurityLogging': enableSecurityLogging,
    };
  }

  // Validate configuration
  static bool validateConfiguration() {
    final errors = <String>[];

    if (apiBaseUrl.isEmpty) {
      errors.add('API_BASE_URL is required');
    }

    if (signalingServerUrl.isEmpty) {
      errors.add('SIGNALING_SERVER_URL is required');
    }

    if (enableRequestSigning && secretKey.isEmpty) {
      errors.add('SECRET_KEY is required when request signing is enabled');
    }

    if (errors.isNotEmpty) {
      throw Exception('Configuration validation failed: ${errors.join(', ')}');
    }

    return true;
  }
}
